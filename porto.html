<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON> - Digital Creator Portfolio</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'electric-blue': '#3B82F6',
                        'electric-blue-dark': '#2563EB',
                    },
                    boxShadow: {
                        'glow': '0 0 15px rgba(59, 130, 246, 0.5)',
                        'glow-lg': '0 0 25px rgba(59, 130, 246, 0.7)'
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0)' },
                            '50%': { transform: 'translateY(-10px)' },
                        }
                    }
                }
            }
        }
    </script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0f172a;
            color: #cbd5e1;
            scroll-behavior: smooth;
        }
        .hero-gradient {
            background: radial-gradient(circle at 70% 30%, rgba(30, 41, 59, 0.5) 0%, rgba(15, 23, 42, 0) 70%);
        }
        .profile-glow {
            box-shadow: 0 0 0 1px rgba(59, 130, 246, 0.3), 0 0 20px rgba(59, 130, 246, 0.5);
        }
        .glass-card {
            background: rgba(30, 41, 59, 0.2);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(71, 85, 105, 0.5);
            transition: all 0.3s ease;
        }
        .glass-card:hover {
            border-color: rgba(59, 130, 246, 0.7);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
            transform: translateY(-5px);
        }
        .service-card:hover {
            border-color: #3B82F6;
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.4);
        }
        .nav-link {
            position: relative;
        }
        .nav-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: #3B82F6;
            transition: width 0.3s ease;
        }
        .nav-link:hover::after {
            width: 100%;
        }
    </style>
</head>
<body class="bg-slate-900 text-slate-300">
    <!-- Navbar -->
    <nav class="fixed w-full z-50 bg-slate-900/80 backdrop-blur-sm border-b border-slate-800">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <a href="#" class="text-xl font-bold text-slate-100 flex items-center">
                        <span class="text-electric-blue">NUSA</span>FLOW
                    </a>
                </div>
                
                <!-- Desktop Menu -->
                <div class="hidden md:flex items-center space-x-8">
                    <a href="#home" class="nav-link text-slate-300 hover:text-white">Beranda</a>
                    <a href="#portfolio" class="nav-link text-slate-300 hover:text-white">Portfolio</a>
                    <a href="#services" class="nav-link text-slate-300 hover:text-white">Layanan</a>
                    <a href="#about" class="nav-link text-slate-300 hover:text-white">Tentang</a>
                    <a href="#contact" class="px-4 py-2 bg-electric-blue text-white rounded-md hover:bg-electric-blue-dark transition">Hubungi</a>
                </div>
                
                <!-- Mobile Menu Button -->
                <div class="md:hidden flex items-center">
                    <button id="mobile-menu-button" class="text-slate-300 hover:text-white focus:outline-none">
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- Mobile Menu -->
        <div id="mobile-menu" class="md:hidden hidden bg-slate-800">
            <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3">
                <a href="#home" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Beranda</a>
                <a href="#portfolio" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Portfolio</a>
                <a href="#services" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Layanan</a>
                <a href="#about" class="block px-3 py-2 rounded-md text-base font-medium text-slate-300 hover:text-white hover:bg-slate-700">Tentang</a>
                <a href="#contact" class="block px-3 py-2 rounded-md text-base font-medium text-white bg-electric-blue">Hubungi</a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section id="home" class="pt-24 pb-16 md:pt-32 md:pb-24 hero-gradient">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex flex-col md:flex-row items-center">
                <!-- Left Column -->
                <div class="md:w-1/2 mb-12 md:mb-0">
                    <div class="text-sm font-medium text-electric-blue mb-4 animate-pulse-slow">AHMAD FAJRI- DIGITAL CREATOR</div>
                    <h1 class="text-4xl md:text-5xl lg:text-6xl font-bold text-slate-100 leading-tight mb-6">
                        Wujudkan Visi Bisnis Anda <span class="text-electric-blue">di Dunia Digital</span>.
                    </h1>
                    <p class="text-lg text-slate-300 mb-8 max-w-lg">
                Saya membantu UMKM memiliki 'rumah digital' pertama yang profesional, modern, dan terjangkau.
                    </p>
                    <div class="flex flex-wrap gap-4">
                        <a href="#portfolio" class="px-8 py-3 bg-electric-blue text-white font-medium rounded-md hover:bg-electric-blue-dark transition transform hover:-translate-y-1 shadow-glow">
                            Lihat Karya Saya
                        </a>
                        <a href="#contact" class="px-8 py-3 border border-slate-600 text-slate-300 font-medium rounded-md hover:bg-slate-800 transition">
                            Hubungi Saya
                        </a>
                    </div>
                </div>
                
                <!-- Right Column -->
                <div class="md:w-1/2 flex justify-center">
                    <div class="relative">
                        <div class="absolute -inset-4 rounded-full bg-electric-blue opacity-20 blur-xl animate-pulse"></div>
                        <div class="relative w-64 h-64 md:w-80 md:h-80 rounded-full overflow-hidden border-4 border-slate-800 profile-glow">
                            <img 
                                  src="avatar.png"
                                  alt="Foto Profil"
                                  class="w-full h-full object-cove" >
                        </div>
                        <div class="absolute top-0 right-0 w-16 h-16 bg-electric-blue rounded-full flex items-center justify-center animate-float">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

<!-- Portfolio Section -->
<section id="portfolio" class="py-16 md:py-24 bg-slate-900/50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-slate-100 mb-4">Proyek Unggulan</h2>
            <p class="text-slate-400 max-w-2xl mx-auto">Solusi digital yang saya kembangkan untuk membantu klien mencapai tujuan bisnis mereka</p>
        </div>
        
        <!-- Kartu Portofolio Utama -->
        <div class="max-w-4xl mx-auto">
            <div class="glass-card rounded-2xl overflow-hidden">
                <div class="p-1 bg-gradient-to-r from-slate-800 to-slate-900 rounded-2xl">
                    <div class="bg-slate-800 p-6 rounded-xl">
                        <div class="bg-gray-200 border-2 border-dashed rounded-xl w-full h-64 md:h-80 flex items-center justify-center mb-6">
                            <span class="text-slate-500">Mockup Website Kopi Senja</span>
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold text-slate-100 mb-2">Kopi Senja - Landing Page Kedai Kopi</h3>
                            <p class="text-slate-400 mb-6">
                             Sebuah landing page yang dirancang untuk menangkap suasana hangat dan estetik dari brand "Kopi Senja". Fokus utamanya adalah menampilkan menu secara elegan dan memudahkan pelanggan untuk memesan langsung via WhatsApp.
                            </p>
                            <div class="flex flex-wrap gap-4">
                                <a href="#" class="px-6 py-2 border border-slate-600 text-slate-300 font-medium rounded-md hover:bg-slate-700/50 transition">
                                    Lihat Situs Live
                                </a>
                                <!-- TOMBOL UBAHAN (Hybrid Approach) -->
                                <a href="#kopi-senja-details" class="px-6 py-2 border border-electric-blue/50 text-electric-blue font-medium rounded-md hover:bg-electric-blue/10 transition flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    Ringkasan Studi Kasus
                                </a>
                                <a href="/case-study/kopi-senja" class="px-6 py-2 bg-electric-blue/90 text-white font-medium rounded-md hover:bg-electric-blue transition flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    Versi Lengkap
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- ==================== -->
<!-- SECTION RINGKASAN BARU (Tambahkan setelah Portfolio Section) -->
<!-- ==================== -->
<section id="kopi-senja-details" class="py-12 bg-slate-800/20 scroll-mt-20">
    <div class="max-w-4xl mx-auto px-4 sm:px-6">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
            <h3 class="text-2xl font-bold text-slate-100 flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-electric-blue mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Ringkasan Proyek Kopi Senja
            </h3>
            <a href="/case-study/kopi-senja" class="text-sm text-electric-blue hover:underline flex items-center">
                Baca studi kasus lengkap
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
            </a>
        </div>

        <!-- Grid Konten Ringkasan -->
        <div class="grid md:grid-cols-2 gap-8">
            <div class="space-y-6">
                <div class="glass-card p-6 rounded-xl">
                    <h4 class="text-lg font-semibold text-electric-blue mb-3">🔍 Tantangan Klien</h4>
                    <ul class="list-disc list-inside text-slate-300 space-y-2 pl-2">
                        <li>Tidak Punya 'Rumah Digital': Promosi hanya tersebar di media sosial tanpa adanya pusat informasi yang profesional.</li>
                        <li>Proses Pemesanan Tidak Efisien: Pemesanan masih manual dan seringkali calon pelanggan bingung dengan menu yang ada.</li>
                        <li>Citra Brand Kurang Kuat: Kesulitan untuk tampil beda dan lebih premium dari pesaing.</li>
                    </ul>
                </div>

                <div class="glass-card p-6 rounded-xl">
                    <h4 class="text-lg font-semibold text-electric-blue mb-3">📅 Timeline</h4>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm mb-1">
                                <span class="text-slate-300">Research</span>
                                <span class="text-slate-400">1 Minggu</span>
                            </div>
                            <div class="w-full bg-slate-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: 20%"></div>
                            </div>
                        </div>
                        <!-- Tambahkan fase lainnya -->
                    </div>
                </div>
            </div>

<!-- Di dalam section #kopi-senja-details -->
<div class="glass-card p-6 rounded-xl">
    <h4 class="text-lg font-semibold text-electric-blue mb-3">🎯 Hasil yang Dicapai</h4>
    <ul class="space-y-4">
        <li class="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
                <span class="font-medium text-slate-100">Citra Brand Lebih Profesional & Terpercaya</span>
                <p class="text-slate-400 text-sm">Desain modern meningkatkan persepsi kualitas produk di mata pelanggan</p>
            </div>
        </li>
        <li class="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
                <span class="font-medium text-slate-100">Pusat Informasi Produk 24/7</span>
                <p class="text-slate-400 text-sm">Pelanggan bisa mengakses menu, harga, dan promo kapan saja tanpa batas waktu</p>
            </div>
        </li>
        <li class="flex items-start">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
            </svg>
            <div>
                <span class="font-medium text-slate-100">Alur Pemesanan Lebih Jelas</span>
                <p class="text-slate-400 text-sm">CTA WhatsApp yang strategis mengurangi kebingungan pelanggan dalam memesan</p>
            </div>
        </li>
    </ul>
</div>
                <div class="glass-card p-6 rounded-xl">
                    <h4 class="text-lg font-semibold text-electric-blue mb-3">🛠 Teknologi Digunakan</h4>
                    <div class="flex flex-wrap gap-2">
                        <span class="px-3 py-1 bg-slate-700 rounded-full text-sm">Figma</span>
                        <span class="px-3 py-1 bg-slate-700 rounded-full text-sm">Tailwind CSS</span>
                        <span class="px-3 py-1 bg-slate-700 rounded-full text-sm">Alpine.js</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

    <!-- Services Section -->
    <section id="services" class="py-16 md:py-24 bg-slate-900">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-slate-100 mb-4">Layanan Unggulan</h2>
                <p class="text-slate-400 max-w-2xl mx-auto">Solusi lengkap untuk transformasi digital bisnis Anda</p>
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <!-- Service Card 1 -->
                <div class="glass-card rounded-xl p-6 border border-slate-700 service-card">
                    <div class="w-14 h-14 rounded-full bg-electric-blue/10 flex items-center justify-center mb-6">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-electric-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-slate-100 mb-3">Landing Page Premium</h3>
                    <p class="text-slate-400 mb-4">
                        Desain landing page modern dengan konversi tinggi yang dirancang khusus untuk meningkatkan penjualan dan lead.
                    </p>
                    <ul class="text-slate-400 space-y-2 text-sm">
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                            </svg>
                            Desain responsif & mobile-friendly
                        </li>
                        <li class="flex items-start">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-electric-blue mr-2 mt-0.5" viewBo